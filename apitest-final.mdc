# 工具API接口测试文档

本文档包含所有API接口的请求参数示例和响应格式示例，按照接口复杂度分为三个阶段。

## 📊 AI模型配置信息

### 🤖 支持的AI平台列表
- **LiblibAI**: 图像生成专业平台
- **KlingAI**: 视频生成领导者
- **MiniMax**: 多模态AI平台
- **DeepSeek**: 剧情生成和分镜脚本专家
- **火山引擎豆包**: 专业语音AI平台

### 🚫 禁止使用的模型
- OpenAI、GPT系列模型
- anthropic、Claude系列模型

### 🎯 业务模型配置矩阵

#### 图像生成业务 - 可选平台: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 专业图像生成、ComfyUI工作流、风格转换
- **KlingAI**: 高质量图像生成、图像放大、图像修复
- **MiniMax**: 多模态图像生成、图像理解

#### 视频生成业务 - 可选平台: KlingAI + MiniMax
- **KlingAI**: 专业视频生成、图像转视频、视频扩展
- **MiniMax**: 多模态视频生成、视频理解

#### 剧情生成业务 - 可选平台: DeepSeek + MiniMax
- **DeepSeek**: 专业剧情创作、分镜脚本、角色对话
- **MiniMax**: 多模态剧情生成、情节构建

#### 角色生成业务 - 可选平台: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 角色形象生成、角色设计
- **KlingAI**: 角色动画生成、角色表情
- **MiniMax**: 角色属性生成、角色对话

#### 风格生成业务 - 可选平台: LiblibAI + KlingAI + MiniMax
- **LiblibAI**: 艺术风格生成、风格转换
- **KlingAI**: 视觉风格生成、风格应用
- **MiniMax**: 多模态风格生成、风格理解

#### 音效生成业务 - 可选平台: 火山引擎豆包 + MiniMax
- **火山引擎豆包**: 专业音效处理、音效合成
- **MiniMax**: 多模态音效生成、音效理解

#### 音色生成业务 - 可选平台: MiniMax + 火山引擎豆包
- **MiniMax**: 音色设计、音色合成
- **火山引擎豆包**: 声音复刻、音色处理

#### 音乐生成业务 - 可选平台: MiniMax
- **MiniMax**: 专业音乐生成、音乐创作、音乐理解

## 统一响应格式

所有API接口遵循统一的响应格式：

### 成功响应格式
```json
{
    "code": 200,                    // 业务状态码（成功）
    "message": "操作成功",           // 响应消息
    "data": {                       // 响应数据
        // 具体业务数据
    },
    "timestamp": **********,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

### 错误响应格式
```json
{
    "code": 1006,                   // 业务错误码
    "message": "积分不足",           // 错误消息
    "data": null,                   // 错误数据（可选）
    "timestamp": **********,        // 时间戳
    "request_id": "req_abc123_def456" // 请求ID
}
```

## 认证机制

API支持两种认证方式：

1. **Bearer Token方式**（推荐）：
   ```
   Authorization: Bearer {token}
   ```

2. **URL参数方式**（兼容性）：
   ```
   ?token={token}
   ```

## 第一阶段：无或轻度数据依赖接口

### AdController (2个接口)

#### 1.1 广告开始 `POST /api/ad/store`

**请求参数示例**：
```json
{
    "ad_type": "banner",
    "position": "home_top",
    "duration": 30,
    "target_audience": {
        "age_range": "18-35",
        "interests": ["ai", "technology"]
    },
    "device_info": {
        "device_id": "d123456789",
        "platform": "ios",
        "os_version": "15.0"
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "广告记录创建成功",
    "data": {
        "ad_id": 12345,
        "start_time": "2023-01-01 12:00:00",
        "estimated_end_time": "2023-01-01 12:00:30",
        "status": "active"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数验证失败**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "ad_type": ["广告类型不能为空"],
            "position": ["广告位置不能为空"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1.2 广告结束 `POST /api/ad/update`

**请求参数示例**：
```json
{
    "ad_id": 12345,
    "end_time": "2023-01-01 12:00:30",
    "interaction_data": {
        "clicks": 5,
        "impressions": 100,
        "completion_rate": 0.85,
        "engagement_time": 25
    },
    "user_feedback": {
        "rating": 4,
        "comments": "内容相关性高"
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "广告数据更新成功",
    "data": {
        "ad_id": 12345,
        "start_time": "2023-01-01 12:00:00",
        "end_time": "2023-01-01 12:00:30",
        "status": "completed",
        "performance": {
            "clicks": 5,
            "impressions": 100,
            "ctr": 0.05,
            "completion_rate": 0.85
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 广告不存在**：
```json
{
    "code": 404,
    "message": "广告不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 1.3 按业务类型获取可选平台 `GET /api/ai-models/business-platforms`

**请求参数示例**：
```json
{
    "business_type": "image",
    "include_alternatives": true
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "business_type": "image",
        "primary_platforms": [
            {
                "id": 3,
                "name": "LiblibAI Image",
                "provider": "LiblibAI",
                "priority": 1,
                "features": ["专业图像生成", "ComfyUI工作流", "风格转换"],
                "cost_per_image": 0.05,
                "max_resolution": "2048x2048"
            }
        ],
        "alternative_platforms": [
            {
                "id": 4,
                "name": "KlingAI Image",
                "provider": "KlingAI",
                "priority": 2,
                "features": ["高质量图像生成", "图像放大", "图像修复"],
                "cost_per_image": 0.04,
                "max_resolution": "1920x1080"
            },
            {
                "id": 2,
                "name": "MiniMax Chat",
                "provider": "MiniMax",
                "priority": 3,
                "features": ["多模态图像生成", "图像理解"],
                "cost_per_image": 0.03,
                "max_resolution": "1024x1024"
            }
        ],
        "platform_switching": {
            "enabled": true,
            "fallback_strategy": "auto",
            "load_balancing": true
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 业务类型不支持**：
```json
{
    "code": 422,
    "message": "不支持的业务类型",
    "data": {
        "business_type": "unsupported_type",
        "supported_types": ["image", "video", "story", "character", "style", "voice", "sound", "music"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AiModelController (6个接口)

#### 2.1 获取可用模型 `GET /api/ai-models/available`

**请求参数示例**：
```json
{
    "type": "text",
    "capability": "generation",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "DeepSeek Chat",
                "type": "text",
                "capability": ["generation", "completion", "chat"],
                "provider": "DeepSeek",
                "status": "available",
                "description": "强大的DeepSeek对话模型",
                "max_tokens": 8192,
                "cost_per_token": 0.02,
                "business_types": ["story", "character_dialogue"],
                "platform_priority": 1
            },
            {
                "id": 2,
                "name": "MiniMax Chat",
                "type": "text",
                "capability": ["generation", "analysis"],
                "provider": "MiniMax",
                "status": "available",
                "description": "MiniMax多模态对话模型",
                "max_tokens": 4096,
                "cost_per_token": 0.015,
                "business_types": ["story", "character_dialogue", "music", "voice", "sound"],
                "platform_priority": 2
            },
            {
                "id": 3,
                "name": "LiblibAI Image",
                "type": "image",
                "capability": ["generation", "style_transfer", "comfyui"],
                "provider": "LiblibAI",
                "status": "available",
                "description": "专业图像生成和ComfyUI工作流",
                "max_resolution": "2048x2048",
                "cost_per_image": 0.05,
                "business_types": ["image", "character", "style"],
                "platform_priority": 1
            },
            {
                "id": 4,
                "name": "KlingAI Image",
                "type": "image",
                "capability": ["generation", "upscale", "repair"],
                "provider": "KlingAI",
                "status": "available",
                "description": "高质量图像生成和处理",
                "max_resolution": "1920x1080",
                "cost_per_image": 0.04,
                "business_types": ["image", "character", "style", "video"],
                "platform_priority": 2
            },
            {
                "id": 5,
                "name": "KlingAI Video",
                "type": "video",
                "capability": ["generation", "image_to_video", "extension"],
                "provider": "KlingAI",
                "status": "available",
                "description": "专业视频生成和图像转视频",
                "max_duration": "10s",
                "cost_per_second": 0.20,
                "business_types": ["video"],
                "platform_priority": 1
            },
            {
                "id": 6,
                "name": "火山引擎豆包 Voice",
                "type": "voice",
                "capability": ["synthesis", "clone", "emotion"],
                "provider": "火山引擎豆包",
                "status": "available",
                "description": "专业语音合成和声音复刻",
                "sample_rate": "24kHz",
                "cost_per_char": 0.008,
                "business_types": ["voice", "sound"],
                "platform_priority": 1
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 15,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.2 获取模型详情 `GET /api/ai-models/{model_id}/detail`

**请求参数示例**：
```json
{
    "model_id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "DeepSeek Chat",
        "type": "text",
        "capability": ["generation", "completion", "chat"],
        "provider": "DeepSeek",
        "status": "available",
        "description": "强大的DeepSeek对话模型，具有优秀的文本生成和理解能力",
        "max_tokens": 8192,
        "cost_per_token": 0.02,
        "parameters": {
            "temperature": {
                "min": 0.0,
                "max": 2.0,
                "default": 1.0
            },
            "top_p": {
                "min": 0.0,
                "max": 1.0,
                "default": 1.0
            }
        },
        "supported_formats": ["text", "json"],
        "rate_limits": {
            "requests_per_minute": 60,
            "tokens_per_minute": 40000
        },
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不存在**：
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.3 获取收藏模型 `GET /api/ai-models/favorites`

**请求参数示例**：
```json
{
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "DeepSeek Chat",
                "type": "text",
                "provider": "DeepSeek",
                "status": "available",
                "favorited_at": "2023-01-01 12:00:00",
                "usage_count": 25
            },
            {
                "id": 3,
                "name": "LiblibAI 图像生成",
                "type": "image",
                "provider": "LiblibAI",
                "status": "available",
                "favorited_at": "2023-01-02 10:30:00",
                "usage_count": 12
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 5,
            "last_page": 1
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.4 模型列表 `GET /api/ai-models/list`

**请求参数示例**：
```json
{
    "category": "text",
    "sort": "popularity",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "DeepSeek Chat",
                "category": "text",
                "provider": "DeepSeek",
                "status": "available",
                "popularity_score": 95,
                "usage_count": 1250,
                "rating": 4.8,
                "description": "强大的DeepSeek对话模型"
            },
            {
                "id": 2,
                "name": "MiniMax Chat",
                "category": "text",
                "provider": "MiniMax",
                "status": "available",
                "popularity_score": 88,
                "usage_count": 890,
                "rating": 4.6,
                "description": "MiniMax多模态对话模型"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 25,
            "last_page": 2
        },
        "categories": ["text", "image", "audio", "video"],
        "sort_options": ["popularity", "rating", "newest", "name"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.5 智能平台切换 `POST /api/ai-models/switch`

**请求参数示例**：
```json
{
    "business_type": "image",
    "preferred_platform": "LiblibAI",
    "fallback_enabled": true,
    "task_config": {
        "style": "realistic",
        "resolution": "1024x1024",
        "quality": "high"
    },
    "cost_optimization": true
}
```

**请求参数示例 - 手动指定模型**：
```json
{
    "model_id": 3,
    "business_type": "image",
    "task_config": {
        "temperature": 0.8,
        "max_tokens": 2048,
        "top_p": 0.9
    }
}
```

**成功响应示例 - 智能平台切换**：
```json
{
    "code": 200,
    "message": "平台切换成功",
    "data": {
        "selected_platform": {
            "id": 3,
            "name": "LiblibAI Image",
            "provider": "LiblibAI",
            "business_type": "image",
            "status": "active",
            "selection_reason": "最佳匹配业务需求"
        },
        "alternative_platforms": [
            {
                "id": 4,
                "name": "KlingAI Image",
                "provider": "KlingAI",
                "availability": "available",
                "estimated_cost": 0.04
            },
            {
                "id": 2,
                "name": "MiniMax Chat",
                "provider": "MiniMax",
                "availability": "available",
                "estimated_cost": 0.03
            }
        ],
        "switch_time": "2023-01-01 12:00:00",
        "config_applied": {
            "style": "realistic",
            "resolution": "1024x1024",
            "quality": "high"
        },
        "cost_estimate": {
            "per_request": 0.05,
            "currency": "USD"
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**成功响应示例 - 自动降级**：
```json
{
    "code": 200,
    "message": "平台自动切换成功",
    "data": {
        "selected_platform": {
            "id": 4,
            "name": "KlingAI Image",
            "provider": "KlingAI",
            "business_type": "image",
            "status": "active",
            "selection_reason": "首选平台不可用，自动降级"
        },
        "fallback_info": {
            "original_platform": "LiblibAI",
            "fallback_reason": "平台维护中",
            "estimated_recovery": "2023-01-01 14:00:00"
        },
        "switch_time": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不存在**：
```json
{
    "code": 404,
    "message": "模型不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 模型不可用**：
```json
{
    "code": 1013,
    "message": "模型不可用",
    "data": {
        "model_id": 2,
        "reason": "模型正在维护中",
        "estimated_available_time": "2023-01-01 14:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 2.6 平台性能对比 `GET /api/ai-models/platform-comparison`

**请求参数示例**：
```json
{
    "business_type": "image",
    "comparison_metrics": ["cost", "quality", "speed", "availability"],
    "time_range": "7d"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "business_type": "image",
        "comparison_data": [
            {
                "platform": "LiblibAI",
                "metrics": {
                    "cost_score": 7.5,
                    "quality_score": 9.2,
                    "speed_score": 8.0,
                    "availability_score": 9.5,
                    "overall_score": 8.6
                },
                "performance_stats": {
                    "avg_generation_time": "45s",
                    "success_rate": "96.8%",
                    "cost_per_image": 0.05,
                    "uptime": "99.2%"
                },
                "recommendation": "最佳质量选择"
            },
            {
                "platform": "KlingAI",
                "metrics": {
                    "cost_score": 8.0,
                    "quality_score": 8.8,
                    "speed_score": 7.5,
                    "availability_score": 9.0,
                    "overall_score": 8.3
                },
                "performance_stats": {
                    "avg_generation_time": "50s",
                    "success_rate": "94.5%",
                    "cost_per_image": 0.04,
                    "uptime": "98.8%"
                },
                "recommendation": "性价比优选"
            },
            {
                "platform": "MiniMax",
                "metrics": {
                    "cost_score": 9.0,
                    "quality_score": 7.8,
                    "speed_score": 9.2,
                    "availability_score": 8.5,
                    "overall_score": 8.1
                },
                "performance_stats": {
                    "avg_generation_time": "30s",
                    "success_rate": "92.3%",
                    "cost_per_image": 0.03,
                    "uptime": "97.5%"
                },
                "recommendation": "速度优先选择"
            }
        ],
        "best_platform": {
            "overall": "LiblibAI",
            "cost_effective": "MiniMax",
            "highest_quality": "LiblibAI",
            "fastest": "MiniMax"
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AssetController (3个接口)

#### 3.1 获取素材列表 `GET /api/assets/list`

**请求参数示例**：
```json
{
    "type": "image",
    "category": "background",
    "search": "风景",
    "page": 1,
    "per_page": 20
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "山水风景.jpg",
                "type": "image",
                "category": "background",
                "url": "https://example.com/assets/landscape1.jpg",
                "thumbnail": "https://example.com/assets/thumbs/landscape1.jpg",
                "size": 2048576,
                "dimensions": {
                    "width": 1920,
                    "height": 1080
                },
                "tags": ["风景", "山水", "自然"],
                "created_at": "2023-01-01 12:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 150,
            "last_page": 8
        },
        "filters": {
            "types": ["image", "video", "audio"],
            "categories": ["background", "character", "effect"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 3.2 获取素材详情 `GET /api/assets/{id}`

**请求参数示例**：
```json
{
    "id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "name": "山水风景.jpg",
        "type": "image",
        "category": "background",
        "url": "https://example.com/assets/landscape1.jpg",
        "thumbnail": "https://example.com/assets/thumbs/landscape1.jpg",
        "size": 2048576,
        "format": "jpeg",
        "dimensions": {
            "width": 1920,
            "height": 1080
        },
        "metadata": {
            "color_palette": ["#2E8B57", "#87CEEB", "#F5DEB3"],
            "dominant_colors": ["green", "blue", "beige"],
            "style": "realistic"
        },
        "tags": ["风景", "山水", "自然", "宁静"],
        "usage_count": 45,
        "download_count": 23,
        "rating": 4.7,
        "license": "commercial",
        "author": "AI Generated",
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 素材不存在**：
```json
{
    "code": 404,
    "message": "素材不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 3.3 删除素材 `DELETE /api/assets/{id}`

**请求参数示例**：
```json
{
    "id": 1
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "素材删除成功",
    "data": {
        "deleted_asset": {
            "id": 1,
            "name": "山水风景.jpg",
            "type": "image"
        },
        "deleted_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 素材不存在**：
```json
{
    "code": 404,
    "message": "素材不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 无权限删除**：
```json
{
    "code": 403,
    "message": "无权限删除该素材",
    "data": {
        "reason": "只能删除自己上传的素材"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### AuthController (7个接口)

#### 4.1 用户注册 `POST /api/register`

**请求参数示例**：
```json
{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "phone": "13800138000",
    "verification_code": "123456",
    "invite_code": "INV123456",
    "agree_terms": true
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user": {
            "id": 1001,
            "username": "testuser",
            "email": "<EMAIL>",
            "phone": "13800138000",
            "avatar": "https://example.com/default-avatar.png",
            "status": "active",
            "created_at": "2023-01-01 12:00:00"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_at": "2023-01-08 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数验证失败**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "email": ["邮箱格式不正确"],
            "password": ["密码长度至少6位"],
            "verification_code": ["验证码错误"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 用户已存在**：
```json
{
    "code": 1002,
    "message": "用户已存在",
    "data": {
        "field": "email",
        "value": "<EMAIL>"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.2 用户登录 `POST /api/login`

**请求参数示例**：
```json
{
    "login": "<EMAIL>",
    "password": "password123",
    "remember": true,
    "device_info": {
        "device_id": "d123456789",
        "platform": "web",
        "user_agent": "Mozilla/5.0..."
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user": {
            "id": 1001,
            "username": "testuser",
            "email": "<EMAIL>",
            "avatar": "https://example.com/avatar/1001.png",
            "vip_level": 1,
            "credits": 1000,
            "last_login": "2023-01-01 12:00:00"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_at": "2023-01-08 12:00:00",
        "permissions": ["user.basic", "content.create"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 登录失败**：
```json
{
    "code": 1001,
    "message": "用户名或密码错误",
    "data": {
        "attempts_remaining": 4,
        "lockout_time": null
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 账户被锁定**：
```json
{
    "code": 1003,
    "message": "账户已被锁定",
    "data": {
        "reason": "多次登录失败",
        "unlock_time": "2023-01-01 13:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.3 用户登出 `POST /api/logout`

**请求参数示例**：
```json
{
    "all_devices": false
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "登出成功",
    "data": {
        "logout_time": "2023-01-01 12:00:00",
        "session_duration": "2小时30分钟"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 未登录**：
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.4 刷新Token `POST /api/refresh`

**请求参数示例**：
```json
{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "Token刷新成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "expires_at": "2023-01-08 12:00:00",
        "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - Token无效**：
```json
{
    "code": 1004,
    "message": "Token无效或已过期",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.5 忘记密码 `POST /api/forgot-password`

**请求参数示例**：
```json
{
    "email": "<EMAIL>",
    "captcha": "ABCD1234"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "重置密码邮件已发送",
    "data": {
        "email": "<EMAIL>",
        "expires_at": "2023-01-01 13:00:00",
        "reset_token_length": 32
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 用户不存在**：
```json
{
    "code": 404,
    "message": "用户不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.6 重置密码 `POST /api/reset-password`

**请求参数示例**：
```json
{
    "token": "abc123def456ghi789jkl012",
    "email": "<EMAIL>",
    "password": "newpassword123",
    "password_confirmation": "newpassword123"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "密码重置成功",
    "data": {
        "reset_time": "2023-01-01 12:00:00",
        "auto_login": false
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 重置Token无效**：
```json
{
    "code": 1005,
    "message": "重置链接无效或已过期",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 4.7 验证Token `GET /api/verify`

**请求参数示例**：
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "Token验证成功",
    "data": {
        "valid": true,
        "user_id": 1001,
        "expires_at": "2023-01-08 12:00:00",
        "permissions": ["user.basic", "content.create"]
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - Token无效**：
```json
{
    "code": 1004,
    "message": "Token无效或已过期",
    "data": {
        "valid": false,
        "reason": "expired"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### ImageController (4个接口)

#### 6.1 智能图像生成 `POST /api/images/generate`

**请求参数示例 - 自动平台选择**：
```json
{
    "prompt": "一个美丽的山水风景，中国水墨画风格",
    "style": "chinese_ink",
    "resolution": "1024x1024",
    "quality": "high",
    "platform_preference": "auto",
    "fallback_enabled": true,
    "cost_optimization": false
}
```

**请求参数示例 - 指定平台**：
```json
{
    "prompt": "一个美丽的山水风景，中国水墨画风格",
    "style": "chinese_ink",
    "resolution": "1024x1024",
    "quality": "high",
    "platform": "LiblibAI",
    "platform_config": {
        "use_comfyui": true,
        "workflow_id": "landscape_v2",
        "style_strength": 0.8
    }
}
```

**成功响应示例 - 平台自动选择**：
```json
{
    "code": 200,
    "message": "图像生成任务创建成功",
    "data": {
        "task_id": "img_task_abc123",
        "selected_platform": {
            "name": "LiblibAI",
            "reason": "最佳匹配中国水墨画风格",
            "estimated_time": "45-60秒",
            "cost": 0.05
        },
        "alternative_platforms": [
            {
                "name": "KlingAI",
                "available": true,
                "estimated_time": "50-70秒",
                "cost": 0.04,
                "switch_reason": "成本更低"
            },
            {
                "name": "MiniMax",
                "available": true,
                "estimated_time": "30-45秒",
                "cost": 0.03,
                "switch_reason": "速度更快"
            }
        ],
        "generation_params": {
            "prompt": "一个美丽的山水风景，中国水墨画风格",
            "style": "chinese_ink",
            "resolution": "1024x1024",
            "quality": "high"
        },
        "status": "queued",
        "created_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**成功响应示例 - 平台降级**：
```json
{
    "code": 200,
    "message": "图像生成任务创建成功（已自动切换平台）",
    "data": {
        "task_id": "img_task_def456",
        "selected_platform": {
            "name": "KlingAI",
            "reason": "LiblibAI暂时不可用，自动切换到备选平台",
            "estimated_time": "50-70秒",
            "cost": 0.04
        },
        "fallback_info": {
            "original_platform": "LiblibAI",
            "fallback_reason": "平台维护中",
            "estimated_recovery": "2023-01-01 14:00:00"
        },
        "status": "queued",
        "created_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 所有平台不可用**：
```json
{
    "code": 1013,
    "message": "所有图像生成平台暂时不可用",
    "data": {
        "unavailable_platforms": [
            {
                "name": "LiblibAI",
                "reason": "平台维护中",
                "estimated_recovery": "2023-01-01 14:00:00"
            },
            {
                "name": "KlingAI",
                "reason": "服务器过载",
                "estimated_recovery": "2023-01-01 12:30:00"
            },
            {
                "name": "MiniMax",
                "reason": "API限额已满",
                "estimated_recovery": "2023-01-01 13:00:00"
            }
        ],
        "retry_after": 1800
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 积分不足**：
```json
{
    "code": 1006,
    "message": "积分不足",
    "data": {
        "required_credits": 50,
        "current_credits": 25,
        "platform_costs": {
            "LiblibAI": 50,
            "KlingAI": 40,
            "MiniMax": 30
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 6.2 获取图像生成状态 `GET /api/images/{task_id}/status`

**请求参数示例**：
```json
{
    "task_id": "img_task_abc123",
    "include_platform_info": true
}
```

**成功响应示例 - 生成中**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "img_task_abc123",
        "status": "processing",
        "progress": 65,
        "platform_info": {
            "name": "LiblibAI",
            "queue_position": 2,
            "estimated_completion": "2023-01-01 12:01:30"
        },
        "generation_params": {
            "prompt": "一个美丽的山水风景，中国水墨画风格",
            "style": "chinese_ink",
            "resolution": "1024x1024"
        },
        "created_at": "2023-01-01 12:00:00",
        "updated_at": "2023-01-01 12:01:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 6.3 平台切换 `POST /api/images/{task_id}/switch-platform`

**请求参数示例**：
```json
{
    "task_id": "img_task_abc123",
    "target_platform": "KlingAI",
    "reason": "cost_optimization",
    "preserve_queue": false
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "平台切换成功",
    "data": {
        "task_id": "img_task_abc123",
        "platform_switch": {
            "from": "LiblibAI",
            "to": "KlingAI",
            "reason": "用户主动切换",
            "cost_difference": -0.01,
            "time_difference": "+10秒"
        },
        "new_status": "queued",
        "estimated_completion": "2023-01-01 12:02:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 6.4 批量图像生成 `POST /api/images/batch-generate`

**请求参数示例**：
```json
{
    "tasks": [
        {
            "prompt": "山水风景",
            "style": "chinese_ink",
            "platform": "LiblibAI"
        },
        {
            "prompt": "现代城市",
            "style": "realistic",
            "platform": "KlingAI"
        },
        {
            "prompt": "抽象艺术",
            "style": "abstract",
            "platform": "auto"
        }
    ],
    "batch_config": {
        "load_balancing": true,
        "cost_optimization": true,
        "max_concurrent": 3
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "批量任务创建成功",
    "data": {
        "batch_id": "batch_img_xyz789",
        "tasks": [
            {
                "task_id": "img_task_001",
                "platform": "LiblibAI",
                "status": "queued",
                "estimated_cost": 0.05
            },
            {
                "task_id": "img_task_002",
                "platform": "KlingAI",
                "status": "queued",
                "estimated_cost": 0.04
            },
            {
                "task_id": "img_task_003",
                "platform": "MiniMax",
                "status": "queued",
                "estimated_cost": 0.03,
                "auto_selected": true
            }
        ],
        "total_cost": 0.12,
        "estimated_completion": "2023-01-01 12:03:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### VideoController (3个接口)

#### 7.1 智能视频生成 `POST /api/videos/generate`

**请求参数示例 - 自动平台选择**：
```json
{
    "prompt": "一只可爱的小猫在花园里玩耍",
    "duration": 5,
    "resolution": "720p",
    "fps": 24,
    "platform_preference": "auto",
    "quality": "high"
}
```

**请求参数示例 - 图像转视频**：
```json
{
    "source_image_url": "https://example.com/cat.jpg",
    "prompt": "让小猫动起来，在花园里玩耍",
    "duration": 5,
    "platform": "KlingAI",
    "motion_config": {
        "motion_strength": 0.8,
        "camera_movement": "static"
    }
}
```

**成功响应示例 - 平台自动选择**：
```json
{
    "code": 200,
    "message": "视频生成任务创建成功",
    "data": {
        "task_id": "video_task_abc123",
        "selected_platform": {
            "name": "KlingAI",
            "reason": "专业视频生成平台，质量最佳",
            "estimated_time": "3-5分钟",
            "cost": 1.00
        },
        "alternative_platforms": [
            {
                "name": "MiniMax",
                "available": true,
                "estimated_time": "2-3分钟",
                "cost": 0.80,
                "switch_reason": "速度更快，成本更低"
            }
        ],
        "generation_params": {
            "prompt": "一只可爱的小猫在花园里玩耍",
            "duration": 5,
            "resolution": "720p",
            "fps": 24
        },
        "status": "queued",
        "queue_position": 3,
        "created_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 视频生成平台不可用**：
```json
{
    "code": 1013,
    "message": "视频生成平台暂时不可用",
    "data": {
        "unavailable_platforms": [
            {
                "name": "KlingAI",
                "reason": "队列已满",
                "estimated_recovery": "2023-01-01 12:30:00"
            },
            {
                "name": "MiniMax",
                "reason": "系统维护",
                "estimated_recovery": "2023-01-01 14:00:00"
            }
        ],
        "retry_after": 1800
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 7.2 获取视频生成状态 `GET /api/videos/{task_id}/status`

**成功响应示例 - 生成完成**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "video_task_abc123",
        "status": "completed",
        "progress": 100,
        "platform_info": {
            "name": "KlingAI",
            "processing_time": "4分32秒",
            "quality_score": 9.2
        },
        "result": {
            "video_url": "https://kling.ai/videos/abc123.mp4",
            "thumbnail_url": "https://kling.ai/thumbs/abc123.jpg",
            "duration": 5.0,
            "resolution": "1280x720",
            "file_size": "15.2MB",
            "format": "mp4"
        },
        "created_at": "2023-01-01 12:00:00",
        "completed_at": "2023-01-01 12:04:32"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 7.3 视频平台性能对比 `GET /api/videos/platform-comparison`

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "comparison_data": [
            {
                "platform": "KlingAI",
                "metrics": {
                    "quality_score": 9.2,
                    "speed_score": 7.5,
                    "cost_score": 6.8,
                    "reliability_score": 9.0,
                    "overall_score": 8.1
                },
                "features": [
                    "专业视频生成",
                    "图像转视频",
                    "视频扩展",
                    "高质量输出"
                ],
                "pricing": {
                    "per_second": 0.20,
                    "per_minute": 12.00
                },
                "limitations": {
                    "max_duration": "10秒",
                    "max_resolution": "1080p"
                }
            },
            {
                "platform": "MiniMax",
                "metrics": {
                    "quality_score": 8.5,
                    "speed_score": 8.8,
                    "cost_score": 8.2,
                    "reliability_score": 8.3,
                    "overall_score": 8.5
                },
                "features": [
                    "多模态视频生成",
                    "快速处理",
                    "成本优化",
                    "智能理解"
                ],
                "pricing": {
                    "per_second": 0.16,
                    "per_minute": 9.60
                },
                "limitations": {
                    "max_duration": "8秒",
                    "max_resolution": "720p"
                }
            }
        ],
        "recommendations": {
            "best_quality": "KlingAI",
            "best_speed": "MiniMax",
            "best_value": "MiniMax",
            "most_reliable": "KlingAI"
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### MusicController (3个接口)

#### 8.1 音乐生成 `POST /api/music/generate`

**请求参数示例**：
```json
{
    "prompt": "一首轻松愉快的钢琴曲，适合下午茶时光",
    "style": "classical_piano",
    "duration": 60,
    "tempo": "moderate",
    "key": "C_major",
    "platform": "MiniMax",
    "quality": "high"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "音乐生成任务创建成功",
    "data": {
        "task_id": "music_task_abc123",
        "platform_info": {
            "name": "MiniMax",
            "reason": "唯一支持音乐生成的平台",
            "estimated_time": "2-3分钟",
            "cost": 0.50,
            "features": [
                "专业音乐生成",
                "多种音乐风格",
                "智能编曲",
                "情感表达"
            ]
        },
        "generation_params": {
            "prompt": "一首轻松愉快的钢琴曲，适合下午茶时光",
            "style": "classical_piano",
            "duration": 60,
            "tempo": "moderate",
            "key": "C_major"
        },
        "status": "queued",
        "queue_position": 1,
        "created_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - MiniMax平台不可用**：
```json
{
    "code": 1013,
    "message": "音乐生成平台暂时不可用",
    "data": {
        "platform": "MiniMax",
        "reason": "系统维护中",
        "estimated_recovery": "2023-01-01 14:00:00",
        "alternative_suggestion": "请稍后重试，音乐生成功能仅支持MiniMax平台"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 8.2 获取音乐生成状态 `GET /api/music/{task_id}/status`

**成功响应示例 - 生成完成**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "music_task_abc123",
        "status": "completed",
        "progress": 100,
        "platform_info": {
            "name": "MiniMax",
            "processing_time": "2分18秒",
            "quality_score": 8.9
        },
        "result": {
            "audio_url": "https://minimax.ai/music/abc123.mp3",
            "waveform_url": "https://minimax.ai/waveforms/abc123.png",
            "duration": 60.0,
            "format": "mp3",
            "bitrate": "320kbps",
            "file_size": "2.4MB",
            "metadata": {
                "key": "C_major",
                "tempo": 120,
                "style": "classical_piano",
                "instruments": ["piano", "strings"]
            }
        },
        "created_at": "2023-01-01 12:00:00",
        "completed_at": "2023-01-01 12:02:18"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 8.3 音乐风格列表 `GET /api/music/styles`

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "platform": "MiniMax",
        "supported_styles": [
            {
                "id": "classical_piano",
                "name": "古典钢琴",
                "description": "优雅的古典钢琴音乐",
                "example_url": "https://minimax.ai/examples/classical_piano.mp3",
                "popularity": 9.2
            },
            {
                "id": "electronic_ambient",
                "name": "电子氛围",
                "description": "现代电子氛围音乐",
                "example_url": "https://minimax.ai/examples/electronic_ambient.mp3",
                "popularity": 8.7
            },
            {
                "id": "folk_acoustic",
                "name": "民谣吉他",
                "description": "温暖的民谣吉他音乐",
                "example_url": "https://minimax.ai/examples/folk_acoustic.mp3",
                "popularity": 8.9
            },
            {
                "id": "jazz_smooth",
                "name": "爵士乐",
                "description": "流畅的爵士乐风格",
                "example_url": "https://minimax.ai/examples/jazz_smooth.mp3",
                "popularity": 8.5
            }
        ],
        "total_styles": 12,
        "platform_features": {
            "ai_composition": true,
            "intelligent_arrangement": true,
            "emotion_expression": true,
            "multi_instrument": true,
            "custom_tempo": true,
            "key_selection": true
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### VoiceController (4个接口)

#### 9.1 智能语音合成 `POST /api/voices/synthesize`

**请求参数示例 - 自动平台选择**：
```json
{
    "text": "欢迎使用我们的AI语音合成服务，希望您有愉快的体验。",
    "voice_style": "professional_female",
    "emotion": "friendly",
    "speed": 1.0,
    "platform_preference": "auto",
    "quality": "high"
}
```

**请求参数示例 - 指定平台**：
```json
{
    "text": "欢迎使用我们的AI语音合成服务，希望您有愉快的体验。",
    "platform": "火山引擎豆包",
    "voice_config": {
        "voice_id": "BV700_streaming",
        "emotion": "friendly",
        "speed": 1.0,
        "pitch": 0.0,
        "volume": 1.0
    }
}
```

**成功响应示例 - 平台自动选择**：
```json
{
    "code": 200,
    "message": "语音合成任务创建成功",
    "data": {
        "task_id": "voice_task_abc123",
        "selected_platform": {
            "name": "火山引擎豆包",
            "reason": "专业语音合成平台，质量最佳",
            "estimated_time": "5-10秒",
            "cost": 0.02,
            "sample_rate": "24kHz"
        },
        "alternative_platforms": [
            {
                "name": "MiniMax",
                "available": true,
                "estimated_time": "3-8秒",
                "cost": 0.015,
                "sample_rate": "16kHz",
                "switch_reason": "速度更快，成本更低"
            }
        ],
        "synthesis_params": {
            "text": "欢迎使用我们的AI语音合成服务，希望您有愉快的体验。",
            "voice_style": "professional_female",
            "emotion": "friendly",
            "estimated_duration": "6秒"
        },
        "status": "processing",
        "created_at": "2023-01-01 12:00:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 9.2 获取语音合成状态 `GET /api/voices/{task_id}/status`

**成功响应示例 - 合成完成**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "task_id": "voice_task_abc123",
        "status": "completed",
        "progress": 100,
        "platform_info": {
            "name": "火山引擎豆包",
            "processing_time": "3.2秒",
            "quality_score": 9.5
        },
        "result": {
            "audio_url": "https://volcengine.ai/voices/abc123.wav",
            "duration": 6.0,
            "format": "wav",
            "sample_rate": "24kHz",
            "file_size": "288KB",
            "voice_metadata": {
                "voice_id": "BV700_streaming",
                "emotion": "friendly",
                "speed": 1.0,
                "clarity_score": 9.8
            }
        },
        "created_at": "2023-01-01 12:00:00",
        "completed_at": "2023-01-01 12:00:03"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 9.3 语音平台对比 `GET /api/voices/platform-comparison`

**成功响应示例**：
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "comparison_data": [
            {
                "platform": "火山引擎豆包",
                "metrics": {
                    "quality_score": 9.5,
                    "speed_score": 8.2,
                    "cost_score": 7.8,
                    "voice_variety_score": 9.0,
                    "overall_score": 8.6
                },
                "features": [
                    "专业语音合成",
                    "声音复刻",
                    "情感音色",
                    "24kHz高质量"
                ],
                "voice_count": 50,
                "languages": ["中文", "英文", "日文"],
                "pricing": {
                    "per_1k_chars": 0.008,
                    "free_quota": 10000
                }
            },
            {
                "platform": "MiniMax",
                "metrics": {
                    "quality_score": 8.8,
                    "speed_score": 9.2,
                    "cost_score": 8.5,
                    "voice_variety_score": 8.3,
                    "overall_score": 8.7
                },
                "features": [
                    "音色设计",
                    "音色合成",
                    "快速处理",
                    "多模态支持"
                ],
                "voice_count": 35,
                "languages": ["中文", "英文"],
                "pricing": {
                    "per_1k_chars": 0.006,
                    "free_quota": 5000
                }
            }
        ],
        "recommendations": {
            "best_quality": "火山引擎豆包",
            "best_speed": "MiniMax",
            "best_value": "MiniMax",
            "most_voices": "火山引擎豆包"
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 9.4 批量语音合成 `POST /api/voices/batch-synthesize`

**请求参数示例**：
```json
{
    "tasks": [
        {
            "text": "第一段文本内容",
            "voice_style": "professional_female",
            "platform": "火山引擎豆包"
        },
        {
            "text": "第二段文本内容",
            "voice_style": "casual_male",
            "platform": "MiniMax"
        },
        {
            "text": "第三段文本内容",
            "voice_style": "auto",
            "platform": "auto"
        }
    ],
    "batch_config": {
        "load_balancing": true,
        "cost_optimization": true,
        "max_concurrent": 5
    }
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "批量语音合成任务创建成功",
    "data": {
        "batch_id": "batch_voice_xyz789",
        "tasks": [
            {
                "task_id": "voice_task_001",
                "platform": "火山引擎豆包",
                "status": "processing",
                "estimated_cost": 0.02
            },
            {
                "task_id": "voice_task_002",
                "platform": "MiniMax",
                "status": "queued",
                "estimated_cost": 0.015
            },
            {
                "task_id": "voice_task_003",
                "platform": "火山引擎豆包",
                "status": "queued",
                "estimated_cost": 0.02,
                "auto_selected": true
            }
        ],
        "total_cost": 0.055,
        "estimated_completion": "2023-01-01 12:01:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

### CaptchaController (3个接口)

#### 5.1 生成验证码 `GET /api/captcha/generate`

**请求参数示例**：
```json
{
    "type": "image",
    "width": 120,
    "height": 40,
    "length": 4,
    "difficulty": "medium"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "验证码生成成功",
    "data": {
        "captcha_id": "cap_abc123def456",
        "image_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expires_at": "2023-01-01 12:05:00",
        "type": "image",
        "length": 4
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 参数错误**：
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "type": ["验证码类型不支持"],
            "length": ["长度必须在4-6之间"]
        }
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 5.2 验证验证码 `POST /api/captcha/verify`

**请求参数示例**：
```json
{
    "captcha_id": "cap_abc123def456",
    "code": "ABCD",
    "case_sensitive": false
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "验证码验证成功",
    "data": {
        "valid": true,
        "captcha_id": "cap_abc123def456",
        "verified_at": "2023-01-01 12:02:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 验证码错误**：
```json
{
    "code": 1007,
    "message": "验证码错误",
    "data": {
        "valid": false,
        "attempts_remaining": 2,
        "captcha_id": "cap_abc123def456"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 验证码过期**：
```json
{
    "code": 1008,
    "message": "验证码已过期",
    "data": {
        "expired_at": "2023-01-01 12:05:00",
        "current_time": "2023-01-01 12:06:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

#### 5.3 刷新验证码 `POST /api/captcha/refresh`

**请求参数示例**：
```json
{
    "captcha_id": "cap_abc123def456",
    "type": "image"
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "验证码刷新成功",
    "data": {
        "captcha_id": "cap_def456ghi789",
        "image_url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "expires_at": "2023-01-01 12:10:00",
        "type": "image",
        "refreshed_at": "2023-01-01 12:05:00"
    },
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 - 验证码不存在**：
```json
{
    "code": 404,
    "message": "验证码不存在",
    "data": null,
    "timestamp": **********,
    "request_id": "req_abc123_def456"
}
```
