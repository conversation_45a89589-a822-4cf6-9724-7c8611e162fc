<?php
/**
 * 测试API修复结果
 */

echo "=== API修复测试 ===\n\n";

// 测试URL列表
$testUrls = [
    'https://api.tiptop.cn/api-controller/AiModelController',
    'https://api.tiptop.cn/api-list',
    'https://api.tiptop.cn/api-data'
];

foreach ($testUrls as $url) {
    echo "测试URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'API Test Script');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "  HTTP状态码: $httpCode\n";
    
    if ($error) {
        echo "  错误: $error\n";
    } else {
        if ($httpCode == 200) {
            echo "  ✅ 成功\n";
            
            // 尝试解析JSON响应
            $data = json_decode($response, true);
            if ($data) {
                echo "  响应类型: JSON\n";
                if (isset($data['success'])) {
                    echo "  成功状态: " . ($data['success'] ? '是' : '否') . "\n";
                }
                if (isset($data['message'])) {
                    echo "  消息: " . $data['message'] . "\n";
                }
                if (isset($data['debug'])) {
                    echo "  调试信息: " . json_encode($data['debug'], JSON_UNESCAPED_UNICODE) . "\n";
                }
            } else {
                echo "  响应类型: HTML/其他\n";
                echo "  响应长度: " . strlen($response) . " 字节\n";
                echo "  响应预览: " . substr($response, 0, 200) . "...\n";
            }
        } else {
            echo "  ❌ 失败\n";
            echo "  响应长度: " . strlen($response) . " 字节\n";
            if (strlen($response) > 0) {
                echo "  响应预览: " . substr($response, 0, 200) . "...\n";
            }
        }
    }
    
    echo "\n";
}

echo "=== 测试完成 ===\n";
