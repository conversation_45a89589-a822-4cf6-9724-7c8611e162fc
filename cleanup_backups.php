<?php
/**
 * 清理备份文件
 */

echo "=== 清理API注释修复备份文件 ===\n\n";

// 控制器目录
$controllerDir = 'app/Http/Controllers/Api';

// 查找所有备份文件
$backupFiles = glob($controllerDir . '/*.backup.*');

if (empty($backupFiles)) {
    echo "✅ 没有找到备份文件\n";
    exit(0);
}

echo "找到 " . count($backupFiles) . " 个备份文件:\n\n";

foreach ($backupFiles as $backupFile) {
    $filename = basename($backupFile);
    echo "删除备份文件: $filename\n";
    
    if (unlink($backupFile)) {
        echo "  ✅ 删除成功\n";
    } else {
        echo "  ❌ 删除失败\n";
    }
}

echo "\n=== 清理完成 ===\n";
echo "已删除所有备份文件\n";
echo "💡 如果需要恢复，请从版本控制系统中恢复\n";
?>
