<?php
/**
 * 验证API注释修复结果
 */

echo "=== 验证API注释修复结果 ===\n\n";

// 控制器目录
$controllerDir = 'app/Http/Controllers/Api';

// 获取所有控制器文件
$files = glob($controllerDir . '/*.php');

if (empty($files)) {
    echo "❌ 未找到控制器文件\n";
    exit(1);
}

echo "检查 " . count($files) . " 个控制器文件\n\n";

$totalErrors = 0;
$totalChecked = 0;
$fixedFiles = [];

foreach ($files as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    
    // 检查是否有备份文件（说明该文件被修复过）
    $backupExists = file_exists($file . '.backup.20250728232058');
    if ($backupExists) {
        $fixedFiles[] = $filename;
    }
    
    // 检查是否还有带引号的API注释
    $patterns = [
        '/(@ApiTitle\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        '/(@ApiSummary\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        '/(@ApiMethod\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        '/(@ApiRoute\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i'
    ];
    
    $fileErrors = 0;
    foreach ($patterns as $pattern) {
        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                echo "❌ $filename: " . trim($match[0]) . "\n";
                $fileErrors++;
            }
        }
    }
    
    if ($fileErrors === 0 && $backupExists) {
        echo "✅ $filename: 已正确修复\n";
    } elseif ($fileErrors === 0) {
        echo "⏭️  $filename: 无需修复\n";
    }
    
    $totalErrors += $fileErrors;
    $totalChecked++;
}

echo "\n=== 统计结果 ===\n";
echo "检查文件数: $totalChecked\n";
echo "修复文件数: " . count($fixedFiles) . "\n";
echo "剩余错误数: $totalErrors\n";

if (count($fixedFiles) > 0) {
    echo "\n已修复的文件:\n";
    foreach ($fixedFiles as $file) {
        echo "  - $file\n";
    }
}

if ($totalErrors === 0) {
    echo "\n🎉 所有API注释引号问题已完全修复！\n";
} else {
    echo "\n⚠️  仍有 $totalErrors 个问题需要处理\n";
}

// 随机抽样验证几个修复后的文件
echo "\n=== 抽样验证 ===\n";
if (count($fixedFiles) > 0) {
    $sampleFiles = array_slice($fixedFiles, 0, 3); // 取前3个
    
    foreach ($sampleFiles as $filename) {
        $file = $controllerDir . '/' . $filename;
        $content = file_get_contents($file);
        
        echo "验证文件: $filename\n";
        
        // 查找API注释示例
        if (preg_match_all('/(@Api(Title|Summary|Method|Route)\s*\([^)]*\))/i', $content, $matches, PREG_SET_ORDER)) {
            $samples = array_slice($matches, 0, 2); // 取前2个示例
            foreach ($samples as $match) {
                echo "  示例: " . trim($match[0]) . "\n";
            }
        }
        echo "\n";
    }
}

echo "=== 验证完成 ===\n";
