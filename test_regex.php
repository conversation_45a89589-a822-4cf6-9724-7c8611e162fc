<?php
/**
 * 测试正则表达式匹配
 */

$testString = '     * @ApiTitle("获取系统配置列表")';

echo "测试字符串: $testString\n\n";

$patterns = [
    '/(@ApiTitle\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
    '/(@ApiTitle\s*\(\s*)([\"\'])([^\"\']*)([\"\'])(\s*\))/i',
    '/(@ApiTitle\s*\(\s*)([\"\'])([^\"\']+)([\"\'])(\s*\))/i',
    '/(@ApiTitle\s*\(\s*)([\"\']).+?([\"\']\s*\))/i'
];

foreach ($patterns as $i => $pattern) {
    echo "模式 " . ($i + 1) . ": $pattern\n";
    
    if (preg_match($pattern, $testString, $matches)) {
        echo "  ✅ 匹配成功\n";
        echo "  完整匹配: " . $matches[0] . "\n";
        if (isset($matches[1])) echo "  组1: " . $matches[1] . "\n";
        if (isset($matches[2])) echo "  组2: " . $matches[2] . "\n";
        if (isset($matches[3])) echo "  组3: " . $matches[3] . "\n";
        if (isset($matches[4])) echo "  组4: " . $matches[4] . "\n";
        if (isset($matches[5])) echo "  组5: " . $matches[5] . "\n";
    } else {
        echo "  ❌ 不匹配\n";
    }
    echo "\n";
}

// 测试实际文件内容
echo "=== 测试实际文件 ===\n";
$file = 'app/Http/Controllers/Api/ConfigController.php';
if (file_exists($file)) {
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    
    foreach ($lines as $lineNum => $line) {
        if (strpos($line, '@ApiTitle') !== false) {
            echo "行 " . ($lineNum + 1) . ": $line\n";
            
            foreach ($patterns as $i => $pattern) {
                if (preg_match($pattern, $line, $matches)) {
                    echo "  模式 " . ($i + 1) . " 匹配: " . $matches[0] . "\n";
                    break;
                }
            }
            echo "\n";
        }
    }
} else {
    echo "文件不存在: $file\n";
}
?>
