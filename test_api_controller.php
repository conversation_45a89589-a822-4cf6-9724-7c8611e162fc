<?php
/**
 * API控制器路径测试脚本
 * 用于诊断 https://api.tiptop.cn/api-controller/AiModelController 500错误
 */

echo "=== API控制器路径测试 ===\n\n";

// 测试不同的路径
$possiblePaths = [
    'php/api/app/Http/Controllers/Api/AiModelController.php',
    '../app/Http/Controllers/Api/AiModelController.php',
    'app/Http/Controllers/Api/AiModelController.php',
    __DIR__ . '/php/api/app/Http/Controllers/Api/AiModelController.php'
];

echo "当前工作目录: " . getcwd() . "\n";
echo "脚本所在目录: " . __DIR__ . "\n\n";

foreach ($possiblePaths as $path) {
    $exists = file_exists($path);
    $readable = $exists ? is_readable($path) : false;
    
    echo "路径: $path\n";
    echo "  存在: " . ($exists ? "是" : "否") . "\n";
    echo "  可读: " . ($readable ? "是" : "否") . "\n";
    
    if ($exists) {
        $size = filesize($path);
        echo "  大小: $size 字节\n";
        
        // 尝试读取文件开头
        $content = file_get_contents($path, false, null, 0, 200);
        if ($content) {
            echo "  内容预览: " . substr(str_replace(["\n", "\r"], " ", $content), 0, 100) . "...\n";
        }
    }
    echo "\n";
}

// 测试Laravel/Lumen函数
echo "=== Laravel/Lumen 函数测试 ===\n";

// 测试 base_path
if (function_exists('base_path')) {
    echo "base_path(): " . base_path() . "\n";
    echo "base_path('app'): " . base_path('app') . "\n";
} else {
    echo "base_path() 函数不存在\n";
}

// 测试 app_path
if (function_exists('app_path')) {
    echo "app_path(): " . app_path() . "\n";
} else {
    echo "app_path() 函数不存在\n";
}

// 测试 app() 函数
if (function_exists('app')) {
    try {
        $app = app();
        if (method_exists($app, 'basePath')) {
            echo "app()->basePath(): " . $app->basePath() . "\n";
            echo "app()->basePath('app'): " . $app->basePath('app') . "\n";
        }
    } catch (Exception $e) {
        echo "app() 函数调用失败: " . $e->getMessage() . "\n";
    }
} else {
    echo "app() 函数不存在\n";
}

echo "\n=== 测试完成 ===\n";
