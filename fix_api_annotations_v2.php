<?php
/**
 * 批量修复API注释中的引号问题 - 版本2
 * 将@ApiTitle、@ApiSummary、@ApiMethod、@ApiRoute后面括号中的双引号和单引号去掉
 */

echo "=== 开始修复API注释引号问题 (版本2) ===\n\n";

// 控制器目录
$controllerDir = 'app/Http/Controllers/Api';

// 获取所有控制器文件
$files = glob($controllerDir . '/*.php');

if (empty($files)) {
    echo "❌ 未找到控制器文件\n";
    exit(1);
}

echo "找到 " . count($files) . " 个控制器文件\n\n";

$totalFixed = 0;
$totalFiles = 0;

foreach ($files as $file) {
    $filename = basename($file);
    echo "处理文件: $filename\n";
    
    $content = file_get_contents($file);
    $originalContent = $content;
    $fileFixed = 0;
    
    // 更精确的正则表达式来匹配API注释
    $patterns = [
        // @ApiTitle("内容") -> @ApiTitle(内容)
        '/(@ApiTitle\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        // @ApiSummary("内容") -> @ApiSummary(内容)
        '/(@ApiSummary\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        // @ApiMethod("内容") -> @ApiMethod(内容)
        '/(@ApiMethod\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        // @ApiRoute("内容") -> @ApiRoute(内容)
        '/(@ApiRoute\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i'
    ];
    
    foreach ($patterns as $pattern) {
        $newContent = preg_replace_callback(
            $pattern,
            function($matches) use (&$fileFixed) {
                $fileFixed++;
                echo "    修复: " . trim($matches[0]) . " -> " . $matches[1] . $matches[3] . $matches[5] . "\n";
                return $matches[1] . $matches[3] . $matches[5];
            },
            $content
        );
        
        if ($newContent !== null) {
            $content = $newContent;
        }
    }
    
    if ($content !== $originalContent) {
        // 备份原文件
        $backupFile = $file . '.backup.v2.' . date('YmdHis');
        copy($file, $backupFile);
        
        // 写入修复后的内容
        file_put_contents($file, $content);
        
        echo "  ✅ 修复了 $fileFixed 个注释\n";
        echo "  📁 备份文件: " . basename($backupFile) . "\n";
        
        $totalFixed += $fileFixed;
        $totalFiles++;
    } else {
        echo "  ⏭️  无需修复\n";
    }
    
    echo "\n";
}

echo "=== 修复完成 ===\n";
echo "处理文件数: " . count($files) . "\n";
echo "修改文件数: $totalFiles\n";
echo "修复注释数: $totalFixed\n";

if ($totalFixed > 0) {
    echo "\n✅ 所有API注释引号问题已修复！\n";
    echo "💡 备份文件已创建，如有问题可以恢复\n";
} else {
    echo "\n✅ 所有文件都已符合规范！\n";
}

echo "\n=== 验证修复结果 ===\n";

// 验证修复结果
$verifyErrors = 0;
foreach ($files as $file) {
    $content = file_get_contents($file);
    
    // 检查是否还有带引号的API注释
    $patterns = [
        '/(@ApiTitle\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        '/(@ApiSummary\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        '/(@ApiMethod\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i',
        '/(@ApiRoute\s*\(\s*)([\"\'])([^\"\']*?)([\"\'])(\s*\))/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content, $matches)) {
            echo "❌ " . basename($file) . " 仍有引号问题: " . trim($matches[0]) . "\n";
            $verifyErrors++;
            break; // 只报告第一个错误
        }
    }
}

if ($verifyErrors === 0) {
    echo "✅ 验证通过！所有文件都已正确修复\n";
} else {
    echo "❌ 发现 $verifyErrors 个文件仍有问题，请检查\n";
}

echo "\n=== 脚本执行完成 ===\n";
