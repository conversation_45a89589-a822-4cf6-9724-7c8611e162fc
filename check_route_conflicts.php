<?php
/**
 * 检查路由冲突的脚本
 */

echo "=== 路由冲突检查 ===\n\n";

// 读取路由文件
$routeFile = 'php/api/routes/web.php';
$content = file_get_contents($routeFile);

// 提取所有路由定义
preg_match_all('/\$router->get\([\'"]([^\'\"]+)[\'"]/', $content, $matches);
$routes = $matches[1];

echo "找到 " . count($routes) . " 个GET路由\n\n";

// 检查冲突
$conflicts = [];
$staticRoutes = [];
$dynamicRoutes = [];

foreach ($routes as $route) {
    if (strpos($route, '{') !== false) {
        // 动态路由
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $route);
        $dynamicRoutes[] = [
            'route' => $route,
            'pattern' => $pattern
        ];
    } else {
        // 静态路由
        $staticRoutes[] = $route;
    }
}

echo "静态路由: " . count($staticRoutes) . " 个\n";
echo "动态路由: " . count($dynamicRoutes) . " 个\n\n";

// 检查静态路由是否被动态路由遮蔽
foreach ($staticRoutes as $staticRoute) {
    foreach ($dynamicRoutes as $dynamicRoute) {
        if (preg_match('#^' . str_replace('/', '\/', $dynamicRoute['pattern']) . '$#', $staticRoute)) {
            $conflicts[] = [
                'static' => $staticRoute,
                'dynamic' => $dynamicRoute['route'],
                'pattern' => $dynamicRoute['pattern']
            ];
        }
    }
}

if (empty($conflicts)) {
    echo "✅ 没有发现路由冲突\n";
} else {
    echo "❌ 发现 " . count($conflicts) . " 个路由冲突:\n\n";
    
    foreach ($conflicts as $i => $conflict) {
        echo ($i + 1) . ". 静态路由 '{$conflict['static']}' 被动态路由 '{$conflict['dynamic']}' 遮蔽\n";
        echo "   动态路由模式: {$conflict['pattern']}\n\n";
    }
}

// 显示一些具体的路由
echo "\n=== 部分路由列表 ===\n";
foreach (array_slice($routes, 0, 20) as $i => $route) {
    $type = strpos($route, '{') !== false ? '[动态]' : '[静态]';
    echo ($i + 1) . ". $type $route\n";
}

if (count($routes) > 20) {
    echo "... 还有 " . (count($routes) - 20) . " 个路由\n";
}

echo "\n=== 检查完成 ===\n";
